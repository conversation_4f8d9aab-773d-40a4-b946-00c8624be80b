#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音视频合并软件启动脚本
"""

import sys
import os
import subprocess
import platform

def check_dependencies():
    """检查依赖项"""
    print("🔍 检查系统依赖...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        return False
    else:
        print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查FFmpeg
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✅ FFmpeg已安装")
        else:
            print("❌ FFmpeg未正确安装")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ 未找到FFmpeg，请先安装FFmpeg")
        print_ffmpeg_install_guide()
        return False
    
    # 检查Python包
    required_packages = [
        'tkinter', 'ffmpeg-python', 'tqdm', 
        'configparser', 'pathlib', 'fuzzywuzzy'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            if package == 'tkinter':
                import tkinter
            elif package == 'ffmpeg-python':
                import ffmpeg
            elif package == 'tqdm':
                import tqdm
            elif package == 'configparser':
                import configparser
            elif package == 'pathlib':
                import pathlib
            elif package == 'fuzzywuzzy':
                import fuzzywuzzy
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少Python包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    else:
        print("✅ 所有Python包已安装")
    
    return True

def print_ffmpeg_install_guide():
    """打印FFmpeg安装指南"""
    system = platform.system().lower()
    
    print("\n📖 FFmpeg安装指南:")
    
    if system == "windows":
        print("Windows:")
        print("1. 访问 https://ffmpeg.org/download.html")
        print("2. 下载Windows版本")
        print("3. 解压到任意目录")
        print("4. 将ffmpeg.exe所在目录添加到系统PATH环境变量")
        
    elif system == "darwin":  # macOS
        print("macOS:")
        print("使用Homebrew安装:")
        print("  brew install ffmpeg")
        print("\n或者使用MacPorts:")
        print("  sudo port install ffmpeg")
        
    elif system == "linux":
        print("Linux:")
        print("Ubuntu/Debian:")
        print("  sudo apt update")
        print("  sudo apt install ffmpeg")
        print("\nCentOS/RHEL:")
        print("  sudo yum install ffmpeg")
        print("\nArch Linux:")
        print("  sudo pacman -S ffmpeg")

def install_requirements():
    """安装Python依赖"""
    print("📦 安装Python依赖包...")
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                      check=True)
        print("✅ 依赖包安装完成")
        return True
    except subprocess.CalledProcessError:
        print("❌ 依赖包安装失败")
        return False

def run_tests():
    """运行测试"""
    print("🧪 运行测试...")
    try:
        # 运行单元测试
        result = subprocess.run([sys.executable, 'test_merger.py'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 所有测试通过")
            return True
        else:
            print("❌ 测试失败:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ 运行测试时出错: {e}")
        return False

def run_manual_test():
    """运行手动测试"""
    print("🔧 运行手动测试...")
    try:
        subprocess.run([sys.executable, 'test_merger.py', 'manual'])
    except Exception as e:
        print(f"❌ 手动测试失败: {e}")

def start_application():
    """启动应用程序"""
    print("🚀 启动音视频合并软件...")
    try:
        subprocess.run([sys.executable, 'audio_video_merger.py'])
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def show_menu():
    """显示菜单"""
    print("\n" + "="*50)
    print("🎬 音视频合并软件")
    print("="*50)
    print("1. 检查系统环境")
    print("2. 安装依赖包")
    print("3. 运行测试")
    print("4. 运行手动测试")
    print("5. 启动软件")
    print("6. 退出")
    print("="*50)

def main():
    """主函数"""
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "check":
            check_dependencies()
        elif command == "install":
            install_requirements()
        elif command == "test":
            run_tests()
        elif command == "manual":
            run_manual_test()
        elif command == "start":
            if check_dependencies():
                start_application()
        else:
            print("未知命令。可用命令: check, install, test, manual, start")
    else:
        # 交互式菜单
        while True:
            show_menu()
            try:
                choice = input("\n请选择操作 (1-6): ").strip()
                
                if choice == "1":
                    check_dependencies()
                elif choice == "2":
                    install_requirements()
                elif choice == "3":
                    run_tests()
                elif choice == "4":
                    run_manual_test()
                elif choice == "5":
                    if check_dependencies():
                        start_application()
                    else:
                        print("\n❌ 环境检查失败，请先解决依赖问题")
                elif choice == "6":
                    print("👋 再见！")
                    break
                else:
                    print("❌ 无效选择，请输入1-6")
                    
                input("\n按回车键继续...")
                
            except KeyboardInterrupt:
                print("\n\n👋 再见！")
                break
            except EOFError:
                break

if __name__ == "__main__":
    main()
