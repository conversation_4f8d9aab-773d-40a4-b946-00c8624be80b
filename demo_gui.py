#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音视频合并软件界面演示
展示新的现代化界面设计
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
from pathlib import Path

class DemoGUI:
    """演示界面"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.setup_styles()
        self.setup_demo_ui()
        
    def setup_styles(self):
        """设置现代化样式"""
        # 配置主题色彩
        self.colors = {
            'primary': '#2E86AB',      # 主色调 - 蓝色
            'secondary': '#A23B72',    # 次要色 - 紫红色
            'success': '#F18F01',      # 成功色 - 橙色
            'background': '#F5F5F5',   # 背景色 - 浅灰
            'surface': '#FFFFFF',      # 表面色 - 白色
            'text': '#333333',         # 文字色 - 深灰
            'text_light': '#666666',   # 浅文字色
            'border': '#E0E0E0',       # 边框色
            'hover': '#E3F2FD'         # 悬停色
        }
        
        # 配置ttk样式
        self.style = ttk.Style()
        self.style.theme_use('clam')
        
        # 配置按钮样式
        self.style.configure('Primary.TButton',
                           background=self.colors['primary'],
                           foreground='white',
                           borderwidth=0,
                           focuscolor='none',
                           padding=(20, 10))
        
        self.style.configure('Secondary.TButton',
                           background=self.colors['secondary'],
                           foreground='white',
                           borderwidth=0,
                           focuscolor='none',
                           padding=(15, 8))
        
        self.style.configure('Success.TButton',
                           background=self.colors['success'],
                           foreground='white',
                           borderwidth=0,
                           focuscolor='none',
                           padding=(15, 8))
        
    def setup_demo_ui(self):
        """设置演示界面"""
        # 窗口基本设置
        self.root.title("🎬 音视频合并工具 - 界面演示")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 700)
        self.root.configure(bg=self.colors['background'])
        
        # 创建主要组件
        self.create_header()
        self.create_demo_content()
        self.create_footer()
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(1, weight=1)
        
    def create_header(self):
        """创建顶部标题区域"""
        header_frame = tk.Frame(self.root, bg=self.colors['primary'], height=80)
        header_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=0, pady=0)
        header_frame.grid_propagate(False)
        
        # 标题
        title_label = tk.Label(header_frame, 
                              text="🎬 音视频合并工具",
                              font=('Microsoft YaHei', 24, 'bold'),
                              fg='white',
                              bg=self.colors['primary'])
        title_label.pack(side=tk.LEFT, padx=30, pady=20)
        
        # 版本信息
        version_label = tk.Label(header_frame,
                               text="v1.0.0 - 界面演示",
                               font=('Microsoft YaHei', 10),
                               fg='white',
                               bg=self.colors['primary'])
        version_label.pack(side=tk.RIGHT, padx=30, pady=20)
        
    def create_demo_content(self):
        """创建演示内容"""
        # 主内容框架
        main_frame = tk.Frame(self.root, bg=self.colors['background'])
        main_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=20, pady=20)
        
        # 左侧面板
        left_panel = tk.Frame(main_frame, bg=self.colors['background'])
        left_panel.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        # 右侧面板
        right_panel = tk.Frame(main_frame, bg=self.colors['background'])
        right_panel.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(10, 0))
        
        # 配置主框架权重
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(0, weight=1)
        
        # 创建演示内容
        self.create_file_selection_demo(left_panel)
        self.create_file_lists_demo(left_panel)
        self.create_match_results_demo(right_panel)
        self.create_settings_demo(right_panel)
        
    def create_file_selection_demo(self, parent):
        """创建文件选择演示区域"""
        selection_frame = ttk.LabelFrame(parent, text="📁 文件选择", padding=20)
        selection_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        
        # 拖拽区域演示
        drag_frame = tk.Frame(selection_frame, bg=self.colors['hover'], 
                             relief='dashed', bd=2, height=100)
        drag_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        drag_frame.grid_propagate(False)
        
        drag_label = tk.Label(drag_frame,
                             text="🎯 拖拽文件夹到此处\n或使用下方按钮选择文件夹",
                             font=('Microsoft YaHei', 12),
                             fg=self.colors['text_light'],
                             bg=self.colors['hover'],
                             justify=tk.CENTER)
        drag_label.pack(expand=True)
        
        # 按钮区域
        button_frame = tk.Frame(selection_frame, bg=self.colors['surface'])
        button_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(15, 0))
        
        ttk.Button(button_frame, text="📹 选择视频文件夹",
                  style='Primary.TButton',
                  command=self.demo_action).grid(row=0, column=0, padx=(0, 10), pady=5)
        
        ttk.Button(button_frame, text="🎵 选择音频文件夹",
                  style='Primary.TButton', 
                  command=self.demo_action).grid(row=0, column=1, padx=(0, 10), pady=5)
        
        ttk.Button(button_frame, text="📂 选择输出文件夹",
                  style='Secondary.TButton',
                  command=self.demo_action).grid(row=0, column=2, pady=5)
        
        button_frame.columnconfigure(0, weight=1)
        button_frame.columnconfigure(1, weight=1)
        button_frame.columnconfigure(2, weight=1)
        
    def create_file_lists_demo(self, parent):
        """创建文件列表演示区域"""
        list_frame = ttk.LabelFrame(parent, text="📋 文件列表", padding=15)
        list_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 15))
        
        # 创建Notebook
        notebook = ttk.Notebook(list_frame)
        notebook.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 视频文件页面
        video_frame = ttk.Frame(notebook)
        notebook.add(video_frame, text="📹 视频文件 (3)")
        
        # 音频文件页面
        audio_frame = ttk.Frame(notebook)
        notebook.add(audio_frame, text="🎵 音频文件 (3)")
        
        # 创建演示数据
        self.create_demo_file_tree(video_frame, "video")
        self.create_demo_file_tree(audio_frame, "audio")
        
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
    def create_demo_file_tree(self, parent, file_type):
        """创建演示文件树"""
        columns = ('name', 'size', 'format', 'path')
        tree = ttk.Treeview(parent, columns=columns, show='headings', height=8)
        
        tree.heading('name', text='文件名')
        tree.heading('size', text='大小')
        tree.heading('format', text='格式')
        tree.heading('path', text='路径')
        
        tree.column('name', width=200)
        tree.column('size', width=80)
        tree.column('format', width=60)
        tree.column('path', width=250)
        
        # 添加演示数据
        if file_type == "video":
            demo_data = [
                ("第一集.mp4", "1.2 GB", "MP4", "/Videos/Series1/"),
                ("Episode_02.avi", "850 MB", "AVI", "/Videos/Series1/"),
                ("电影_上集.mkv", "2.1 GB", "MKV", "/Videos/Movies/")
            ]
        else:
            demo_data = [
                ("第一集.mp3", "45 MB", "MP3", "/Audios/Series1/"),
                ("episode_02.wav", "120 MB", "WAV", "/Audios/Series1/"),
                ("电影 上集.aac", "38 MB", "AAC", "/Audios/Movies/")
            ]
            
        for data in demo_data:
            tree.insert('', 'end', values=data)
        
        scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        
        tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        parent.columnconfigure(0, weight=1)
        parent.rowconfigure(0, weight=1)
        
    def create_match_results_demo(self, parent):
        """创建匹配结果演示区域"""
        match_frame = ttk.LabelFrame(parent, text="🔗 匹配结果", padding=15)
        match_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 15))
        
        # 统计信息
        stats_frame = tk.Frame(match_frame, bg=self.colors['surface'])
        stats_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        stats_info = [
            ("📹 视频文件", "3"),
            ("🎵 音频文件", "3"),
            ("✅ 匹配成功", "3"),
            ("❌ 未匹配", "0")
        ]
        
        for i, (label, value) in enumerate(stats_info):
            frame = tk.Frame(stats_frame, bg=self.colors['hover'], 
                           relief='solid', bd=1, padx=10, pady=5)
            frame.grid(row=0, column=i, padx=5, sticky=(tk.W, tk.E))
            
            tk.Label(frame, text=label, font=('Microsoft YaHei', 9),
                    fg=self.colors['text'], bg=self.colors['hover']).pack()
            
            tk.Label(frame, text=value, font=('Microsoft YaHei', 14, 'bold'),
                    fg=self.colors['primary'], bg=self.colors['hover']).pack()
            
        for i in range(4):
            stats_frame.columnconfigure(i, weight=1)
        
        # 匹配结果树
        match_columns = ('video_name', 'audio_name', 'similarity', 'status')
        match_tree = ttk.Treeview(match_frame, columns=match_columns, 
                                show='headings', height=10)
        
        match_tree.heading('video_name', text='视频文件')
        match_tree.heading('audio_name', text='音频文件')
        match_tree.heading('similarity', text='相似度')
        match_tree.heading('status', text='状态')
        
        match_tree.column('video_name', width=150)
        match_tree.column('audio_name', width=150)
        match_tree.column('similarity', width=80)
        match_tree.column('status', width=80)
        
        # 添加演示匹配结果
        demo_matches = [
            ("第一集.mp4", "第一集.mp3", "100%", "✅ 匹配"),
            ("Episode_02.avi", "episode_02.wav", "95%", "✅ 匹配"),
            ("电影_上集.mkv", "电影 上集.aac", "88%", "✅ 匹配")
        ]
        
        for match in demo_matches:
            match_tree.insert('', 'end', values=match)
        
        match_scrollbar = ttk.Scrollbar(match_frame, orient=tk.VERTICAL, 
                                      command=match_tree.yview)
        match_tree.configure(yscrollcommand=match_scrollbar.set)
        
        match_tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        match_scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))
        
        # 操作按钮
        button_frame = tk.Frame(match_frame, bg=self.colors['surface'])
        button_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        ttk.Button(button_frame, text="🔍 开始匹配",
                  style='Primary.TButton',
                  command=self.demo_action).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="🚀 开始合并",
                  style='Success.TButton',
                  command=self.demo_action).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="🗑️ 清空列表",
                  style='Secondary.TButton',
                  command=self.demo_action).pack(side=tk.LEFT)
        
        match_frame.columnconfigure(0, weight=1)
        match_frame.rowconfigure(1, weight=1)
        
    def create_settings_demo(self, parent):
        """创建设置演示区域"""
        settings_frame = ttk.LabelFrame(parent, text="⚙️ 设置选项", padding=15)
        settings_frame.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # 质量设置
        quality_frame = tk.LabelFrame(settings_frame, text="输出质量", 
                                    bg=self.colors['surface'],
                                    fg=self.colors['text'],
                                    font=('Microsoft YaHei', 10, 'bold'))
        quality_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 输出格式
        tk.Label(quality_frame, text="输出格式:", 
                bg=self.colors['surface']).grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        format_combo = ttk.Combobox(quality_frame, values=["mp4", "avi", "mkv"], 
                                  state="readonly", width=10)
        format_combo.set("mp4")
        format_combo.grid(row=0, column=1, padx=5, pady=2)
        
        # 匹配设置
        matching_frame = tk.LabelFrame(settings_frame, text="匹配设置", 
                                     bg=self.colors['surface'],
                                     fg=self.colors['text'],
                                     font=('Microsoft YaHei', 10, 'bold'))
        matching_frame.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # 匹配阈值
        tk.Label(matching_frame, text="匹配阈值:", 
                bg=self.colors['surface']).grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        threshold_scale = tk.Scale(matching_frame, from_=50, to=100, 
                                 orient=tk.HORIZONTAL, bg=self.colors['surface'], 
                                 length=150)
        threshold_scale.set(80)
        threshold_scale.grid(row=0, column=1, padx=5, pady=2)
        
    def create_footer(self):
        """创建底部状态区域"""
        footer_frame = tk.Frame(self.root, bg=self.colors['surface'], height=120)
        footer_frame.grid(row=2, column=0, sticky=(tk.W, tk.E))
        footer_frame.grid_propagate(False)
        
        # 进度区域
        progress_frame = tk.Frame(footer_frame, bg=self.colors['surface'])
        progress_frame.pack(fill=tk.X, padx=20, pady=10)
        
        progress_label = tk.Label(progress_frame, text="演示模式 - 所有功能正常",
                                font=('Microsoft YaHei', 11),
                                fg=self.colors['text'],
                                bg=self.colors['surface'])
        progress_label.pack(anchor=tk.W)
        
        # 进度条
        progress_var = tk.DoubleVar(value=75)
        progress_bar = ttk.Progressbar(progress_frame, variable=progress_var,
                                     length=400, height=20)
        progress_bar.pack(fill=tk.X, pady=(5, 0))
        
        # 状态信息
        status_frame = tk.Frame(footer_frame, bg=self.colors['surface'])
        status_frame.pack(fill=tk.X, padx=20, pady=(0, 10))
        
        status_label = tk.Label(status_frame, text="🟢 界面演示模式",
                              font=('Microsoft YaHei', 10),
                              fg=self.colors['text_light'],
                              bg=self.colors['surface'])
        status_label.pack(side=tk.LEFT)
        
        time_label = tk.Label(status_frame, text="预计剩余时间: 02:30",
                            font=('Microsoft YaHei', 10),
                            fg=self.colors['text_light'],
                            bg=self.colors['surface'])
        time_label.pack(side=tk.RIGHT)
        
    def demo_action(self):
        """演示动作"""
        messagebox.showinfo("演示模式", "这是界面演示模式\n实际功能请运行完整版本的软件")
        
    def run(self):
        """运行演示"""
        self.root.mainloop()

def main():
    """主函数"""
    print("🎬 启动音视频合并工具界面演示...")
    demo = DemoGUI()
    demo.run()

if __name__ == "__main__":
    main()
