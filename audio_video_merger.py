#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音视频合并软件
支持智能文件名匹配和批量处理
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import queue
import logging
from pathlib import Path
import configparser
from typing import List, Tuple, Dict, Optional
import ffmpeg
from tqdm import tqdm
import difflib
from fuzzywuzzy import fuzz, process

class AudioVideoMerger:
    """音视频合并核心类"""
    
    def __init__(self):
        self.setup_logging()
        self.config = self.load_config()
        
    def setup_logging(self):
        """设置日志系统"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('merger.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def load_config(self) -> configparser.ConfigParser:
        """加载配置文件"""
        config = configparser.ConfigParser()
        config_file = 'config.ini'
        
        if not os.path.exists(config_file):
            # 创建默认配置
            config['DEFAULT'] = {
                'output_format': 'mp4',
                'video_codec': 'libx264',
                'audio_codec': 'aac',
                'quality': 'medium',
                'match_threshold': '80'
            }
            config['MATCHING'] = {
                'ignore_case': 'True',
                'remove_extensions': 'True',
                'fuzzy_matching': 'True'
            }
            
            with open(config_file, 'w', encoding='utf-8') as f:
                config.write(f)
                
        config.read(config_file, encoding='utf-8')
        return config
        
    def find_matching_files(self, video_files: List[str], audio_files: List[str]) -> List[Tuple[str, str]]:
        """智能匹配音频和视频文件"""
        matches = []
        threshold = int(self.config['DEFAULT']['match_threshold'])
        ignore_case = self.config.getboolean('MATCHING', 'ignore_case')
        remove_ext = self.config.getboolean('MATCHING', 'remove_extensions')
        fuzzy = self.config.getboolean('MATCHING', 'fuzzy_matching')
        
        self.logger.info(f"开始匹配文件，阈值: {threshold}")
        
        for video_file in video_files:
            video_name = Path(video_file).stem
            if ignore_case:
                video_name = video_name.lower()
                
            best_match = None
            best_score = 0
            
            for audio_file in audio_files:
                audio_name = Path(audio_file).stem
                if ignore_case:
                    audio_name = audio_name.lower()
                
                # 精确匹配
                if video_name == audio_name:
                    matches.append((video_file, audio_file))
                    self.logger.info(f"精确匹配: {video_file} <-> {audio_file}")
                    break
                    
                # 模糊匹配
                if fuzzy:
                    score = fuzz.ratio(video_name, audio_name)
                    if score > best_score and score >= threshold:
                        best_score = score
                        best_match = audio_file
                        
            else:  # 如果没有精确匹配，使用最佳模糊匹配
                if best_match:
                    matches.append((video_file, best_match))
                    self.logger.info(f"模糊匹配 ({best_score}%): {video_file} <-> {best_match}")
                else:
                    self.logger.warning(f"未找到匹配的音频文件: {video_file}")
                    
        return matches
        
    def merge_audio_video(self, video_path: str, audio_path: str, output_path: str, 
                         progress_callback=None) -> bool:
        """合并单个音频和视频文件"""
        try:
            self.logger.info(f"开始合并: {video_path} + {audio_path} -> {output_path}")
            
            # 获取配置参数
            video_codec = self.config['DEFAULT']['video_codec']
            audio_codec = self.config['DEFAULT']['audio_codec']
            
            # 使用ffmpeg合并
            input_video = ffmpeg.input(video_path)
            input_audio = ffmpeg.input(audio_path)
            
            out = ffmpeg.output(
                input_video['v'], input_audio['a'], 
                output_path,
                vcodec=video_codec,
                acodec=audio_codec,
                **{'c:v': video_codec, 'c:a': audio_codec}
            )
            
            ffmpeg.run(out, overwrite_output=True, quiet=True)
            
            if progress_callback:
                progress_callback()
                
            self.logger.info(f"合并完成: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"合并失败 {video_path} + {audio_path}: {str(e)}")
            return False
            
    def batch_merge(self, matches: List[Tuple[str, str]], output_dir: str, 
                   progress_callback=None) -> Dict[str, bool]:
        """批量合并文件"""
        results = {}
        total = len(matches)
        
        self.logger.info(f"开始批量处理 {total} 个文件对")
        
        for i, (video_file, audio_file) in enumerate(matches):
            video_name = Path(video_file).stem
            output_format = self.config['DEFAULT']['output_format']
            output_file = os.path.join(output_dir, f"{video_name}_merged.{output_format}")
            
            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)
            
            success = self.merge_audio_video(video_file, audio_file, output_file)
            results[f"{video_file}+{audio_file}"] = success
            
            if progress_callback:
                progress_callback(i + 1, total)
                
        self.logger.info(f"批量处理完成，成功: {sum(results.values())}/{total}")
        return results


class MergerGUI:
    """现代化图形用户界面"""

    def __init__(self):
        self.merger = AudioVideoMerger()
        self.root = tk.Tk()
        self.setup_styles()
        self.setup_ui()
        self.video_files = []
        self.audio_files = []
        self.matches = []
        self.output_folder = ""

    def setup_styles(self):
        """设置现代化样式"""
        # 配置主题色彩
        self.colors = {
            'primary': '#2E86AB',      # 主色调 - 蓝色
            'secondary': '#A23B72',    # 次要色 - 紫红色
            'success': '#F18F01',      # 成功色 - 橙色
            'background': '#F5F5F5',   # 背景色 - 浅灰
            'surface': '#FFFFFF',      # 表面色 - 白色
            'text': '#333333',         # 文字色 - 深灰
            'text_light': '#666666',   # 浅文字色
            'border': '#E0E0E0',       # 边框色
            'hover': '#E3F2FD'         # 悬停色
        }

        # 配置ttk样式
        self.style = ttk.Style()
        self.style.theme_use('clam')

        # 配置按钮样式
        self.style.configure('Primary.TButton',
                           background=self.colors['primary'],
                           foreground='white',
                           borderwidth=0,
                           focuscolor='none',
                           padding=(20, 10))

        self.style.configure('Secondary.TButton',
                           background=self.colors['secondary'],
                           foreground='white',
                           borderwidth=0,
                           focuscolor='none',
                           padding=(15, 8))

        self.style.configure('Success.TButton',
                           background=self.colors['success'],
                           foreground='white',
                           borderwidth=0,
                           focuscolor='none',
                           padding=(15, 8))

        # 配置标签框样式
        self.style.configure('Card.TLabelFrame',
                           background=self.colors['surface'],
                           borderwidth=1,
                           relief='solid')

        # 配置进度条样式
        self.style.configure('Modern.Horizontal.TProgressbar',
                           background=self.colors['primary'],
                           troughcolor=self.colors['border'],
                           borderwidth=0,
                           lightcolor=self.colors['primary'],
                           darkcolor=self.colors['primary'])

    def setup_ui(self):
        """设置现代化用户界面"""
        # 窗口基本设置
        self.root.title("🎬 音视频合并工具")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 700)
        self.root.configure(bg=self.colors['background'])

        # 设置窗口图标（如果有的话）
        try:
            # 这里可以添加图标文件
            pass
        except:
            pass

        # 创建主容器
        self.create_header()
        self.create_main_content()
        self.create_footer()

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(1, weight=1)

    def create_header(self):
        """创建顶部标题区域"""
        header_frame = tk.Frame(self.root, bg=self.colors['primary'], height=80)
        header_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=0, pady=0)
        header_frame.grid_propagate(False)

        # 标题
        title_label = tk.Label(header_frame,
                              text="🎬 音视频合并工具",
                              font=('Microsoft YaHei', 24, 'bold'),
                              fg='white',
                              bg=self.colors['primary'])
        title_label.pack(side=tk.LEFT, padx=30, pady=20)

        # 版本信息
        version_label = tk.Label(header_frame,
                               text="v1.0.0",
                               font=('Microsoft YaHei', 10),
                               fg='white',
                               bg=self.colors['primary'])
        version_label.pack(side=tk.RIGHT, padx=30, pady=20)

    def create_main_content(self):
        """创建主要内容区域"""
        # 主内容框架
        main_frame = tk.Frame(self.root, bg=self.colors['background'])
        main_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=20, pady=20)

        # 左侧面板
        left_panel = tk.Frame(main_frame, bg=self.colors['background'])
        left_panel.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))

        # 右侧面板
        right_panel = tk.Frame(main_frame, bg=self.colors['background'])
        right_panel.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(10, 0))

        # 配置主框架权重
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(0, weight=1)

        # 创建左侧内容
        self.create_file_selection(left_panel)
        self.create_file_lists(left_panel)

        # 创建右侧内容
        self.create_match_results(right_panel)
        self.create_settings_panel(right_panel)

    def create_file_selection(self, parent):
        """创建文件选择区域"""
        # 文件选择卡片
        selection_frame = ttk.LabelFrame(parent, text="📁 文件选择",
                                       style='Card.TLabelFrame', padding=20)
        selection_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 15))

        # 创建拖拽区域
        self.create_drag_drop_area(selection_frame)

        # 按钮区域
        button_frame = tk.Frame(selection_frame, bg=self.colors['surface'])
        button_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(15, 0))

        # 文件夹选择按钮
        video_btn = ttk.Button(button_frame, text="📹 选择视频文件夹",
                              style='Primary.TButton',
                              command=self.select_video_folder)
        video_btn.grid(row=0, column=0, padx=(0, 10), pady=5)

        audio_btn = ttk.Button(button_frame, text="🎵 选择音频文件夹",
                              style='Primary.TButton',
                              command=self.select_audio_folder)
        audio_btn.grid(row=0, column=1, padx=(0, 10), pady=5)

        output_btn = ttk.Button(button_frame, text="📂 选择输出文件夹",
                               style='Secondary.TButton',
                               command=self.select_output_folder)
        output_btn.grid(row=0, column=2, pady=5)

        # 配置按钮框架
        button_frame.columnconfigure(0, weight=1)
        button_frame.columnconfigure(1, weight=1)
        button_frame.columnconfigure(2, weight=1)

    def create_drag_drop_area(self, parent):
        """创建拖拽区域"""
        drag_frame = tk.Frame(parent, bg=self.colors['hover'],
                             relief='dashed', bd=2, height=100)
        drag_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        drag_frame.grid_propagate(False)

        # 拖拽提示
        drag_label = tk.Label(drag_frame,
                             text="🎯 拖拽文件夹到此处\n或使用下方按钮选择文件夹",
                             font=('Microsoft YaHei', 12),
                             fg=self.colors['text_light'],
                             bg=self.colors['hover'],
                             justify=tk.CENTER)
        drag_label.pack(expand=True)

        # 绑定拖拽事件（这里可以添加拖拽功能）
        self.setup_drag_drop(drag_frame)

    def setup_drag_drop(self, widget):
        """设置拖拽功能"""
        # 这里可以添加拖拽功能的实现
        # 由于tkinter的拖拽功能比较复杂，这里先预留接口
        pass

    def create_file_lists(self, parent):
        """创建文件列表区域"""
        # 文件列表卡片
        list_frame = ttk.LabelFrame(parent, text="📋 文件列表",
                                  style='Card.TLabelFrame', padding=15)
        list_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 15))

        # 创建Notebook来分页显示
        self.notebook = ttk.Notebook(list_frame)
        self.notebook.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 视频文件页面
        video_frame = ttk.Frame(self.notebook)
        self.notebook.add(video_frame, text="📹 视频文件")

        # 音频文件页面
        audio_frame = ttk.Frame(self.notebook)
        self.notebook.add(audio_frame, text="🎵 音频文件")

        # 创建视频文件列表
        self.create_file_list_with_info(video_frame, "video")

        # 创建音频文件列表
        self.create_file_list_with_info(audio_frame, "audio")

        # 配置列表框架权重
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)

    def create_file_list_with_info(self, parent, file_type):
        """创建带信息的文件列表"""
        # 创建Treeview来显示文件信息
        columns = ('name', 'size', 'format', 'path')
        tree = ttk.Treeview(parent, columns=columns, show='headings', height=10)

        # 设置列标题
        tree.heading('name', text='文件名')
        tree.heading('size', text='大小')
        tree.heading('format', text='格式')
        tree.heading('path', text='路径')

        # 设置列宽
        tree.column('name', width=200)
        tree.column('size', width=80)
        tree.column('format', width=60)
        tree.column('path', width=300)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        # 布局
        tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 配置权重
        parent.columnconfigure(0, weight=1)
        parent.rowconfigure(0, weight=1)

        # 保存引用
        if file_type == "video":
            self.video_tree = tree
        else:
            self.audio_tree = tree

    def create_match_results(self, parent):
        """创建匹配结果区域"""
        # 匹配结果卡片
        match_frame = ttk.LabelFrame(parent, text="🔗 匹配结果",
                                   style='Card.TLabelFrame', padding=15)
        match_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 15))

        # 匹配统计信息
        stats_frame = tk.Frame(match_frame, bg=self.colors['surface'])
        stats_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # 统计标签
        self.stats_labels = {}
        stats_info = [
            ("total_videos", "📹 视频文件", "0"),
            ("total_audios", "🎵 音频文件", "0"),
            ("matched_pairs", "✅ 匹配成功", "0"),
            ("unmatched", "❌ 未匹配", "0")
        ]

        for i, (key, label, value) in enumerate(stats_info):
            frame = tk.Frame(stats_frame, bg=self.colors['hover'],
                           relief='solid', bd=1, padx=10, pady=5)
            frame.grid(row=0, column=i, padx=5, sticky=(tk.W, tk.E))

            tk.Label(frame, text=label, font=('Microsoft YaHei', 9),
                    fg=self.colors['text'], bg=self.colors['hover']).pack()

            self.stats_labels[key] = tk.Label(frame, text=value,
                                            font=('Microsoft YaHei', 14, 'bold'),
                                            fg=self.colors['primary'],
                                            bg=self.colors['hover'])
            self.stats_labels[key].pack()

        # 配置统计框架权重
        for i in range(4):
            stats_frame.columnconfigure(i, weight=1)

        # 匹配结果树形视图
        match_columns = ('video_name', 'audio_name', 'similarity', 'status')
        self.match_tree = ttk.Treeview(match_frame, columns=match_columns,
                                     show='headings', height=12)

        # 设置列标题和宽度
        self.match_tree.heading('video_name', text='视频文件')
        self.match_tree.heading('audio_name', text='音频文件')
        self.match_tree.heading('similarity', text='相似度')
        self.match_tree.heading('status', text='状态')

        self.match_tree.column('video_name', width=180)
        self.match_tree.column('audio_name', width=180)
        self.match_tree.column('similarity', width=80)
        self.match_tree.column('status', width=80)

        # 添加滚动条
        match_scrollbar = ttk.Scrollbar(match_frame, orient=tk.VERTICAL,
                                      command=self.match_tree.yview)
        self.match_tree.configure(yscrollcommand=match_scrollbar.set)

        # 布局匹配结果
        self.match_tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        match_scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))

        # 操作按钮
        match_button_frame = tk.Frame(match_frame, bg=self.colors['surface'])
        match_button_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))

        ttk.Button(match_button_frame, text="🔍 开始匹配",
                  style='Primary.TButton',
                  command=self.start_matching).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(match_button_frame, text="🚀 开始合并",
                  style='Success.TButton',
                  command=self.start_merging).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(match_button_frame, text="🗑️ 清空列表",
                  style='Secondary.TButton',
                  command=self.clear_lists).pack(side=tk.LEFT)

        # 配置匹配框架权重
        match_frame.columnconfigure(0, weight=1)
        match_frame.rowconfigure(1, weight=1)

    def create_settings_panel(self, parent):
        """创建设置面板"""
        # 设置卡片
        settings_frame = ttk.LabelFrame(parent, text="⚙️ 设置选项",
                                      style='Card.TLabelFrame', padding=15)
        settings_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 15))

        # 创建设置选项
        self.create_quality_settings(settings_frame)
        self.create_matching_settings(settings_frame)

    def create_quality_settings(self, parent):
        """创建质量设置"""
        quality_frame = tk.LabelFrame(parent, text="输出质量",
                                    bg=self.colors['surface'],
                                    fg=self.colors['text'],
                                    font=('Microsoft YaHei', 10, 'bold'))
        quality_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # 输出格式选择
        tk.Label(quality_frame, text="输出格式:",
                bg=self.colors['surface']).grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)

        self.format_var = tk.StringVar(value="mp4")
        format_combo = ttk.Combobox(quality_frame, textvariable=self.format_var,
                                  values=["mp4", "avi", "mkv", "mov"],
                                  state="readonly", width=10)
        format_combo.grid(row=0, column=1, padx=5, pady=2)

        # 视频编码器选择
        tk.Label(quality_frame, text="视频编码:",
                bg=self.colors['surface']).grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)

        self.video_codec_var = tk.StringVar(value="libx264")
        video_combo = ttk.Combobox(quality_frame, textvariable=self.video_codec_var,
                                 values=["libx264", "libx265", "copy"],
                                 state="readonly", width=10)
        video_combo.grid(row=1, column=1, padx=5, pady=2)

        # 音频编码器选择
        tk.Label(quality_frame, text="音频编码:",
                bg=self.colors['surface']).grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)

        self.audio_codec_var = tk.StringVar(value="aac")
        audio_combo = ttk.Combobox(quality_frame, textvariable=self.audio_codec_var,
                                 values=["aac", "mp3", "copy"],
                                 state="readonly", width=10)
        audio_combo.grid(row=2, column=1, padx=5, pady=2)

    def create_matching_settings(self, parent):
        """创建匹配设置"""
        matching_frame = tk.LabelFrame(parent, text="匹配设置",
                                     bg=self.colors['surface'],
                                     fg=self.colors['text'],
                                     font=('Microsoft YaHei', 10, 'bold'))
        matching_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # 匹配阈值
        tk.Label(matching_frame, text="匹配阈值:",
                bg=self.colors['surface']).grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)

        self.threshold_var = tk.IntVar(value=80)
        threshold_scale = tk.Scale(matching_frame, from_=50, to=100,
                                 orient=tk.HORIZONTAL, variable=self.threshold_var,
                                 bg=self.colors['surface'], length=150)
        threshold_scale.grid(row=0, column=1, padx=5, pady=2)

        # 匹配选项
        self.ignore_case_var = tk.BooleanVar(value=True)
        ignore_case_check = tk.Checkbutton(matching_frame, text="忽略大小写",
                                         variable=self.ignore_case_var,
                                         bg=self.colors['surface'])
        ignore_case_check.grid(row=1, column=0, columnspan=2, sticky=tk.W, padx=5, pady=2)

        self.fuzzy_match_var = tk.BooleanVar(value=True)
        fuzzy_check = tk.Checkbutton(matching_frame, text="启用模糊匹配",
                                   variable=self.fuzzy_match_var,
                                   bg=self.colors['surface'])
        fuzzy_check.grid(row=2, column=0, columnspan=2, sticky=tk.W, padx=5, pady=2)

    def create_footer(self):
        """创建底部状态区域"""
        footer_frame = tk.Frame(self.root, bg=self.colors['surface'], height=120)
        footer_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), padx=0, pady=0)
        footer_frame.grid_propagate(False)

        # 进度区域
        progress_frame = tk.Frame(footer_frame, bg=self.colors['surface'])
        progress_frame.pack(fill=tk.X, padx=20, pady=10)

        # 进度标签
        self.progress_label = tk.Label(progress_frame, text="准备就绪",
                                     font=('Microsoft YaHei', 11),
                                     fg=self.colors['text'],
                                     bg=self.colors['surface'])
        self.progress_label.pack(anchor=tk.W)

        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame,
                                          variable=self.progress_var,
                                          style='Modern.Horizontal.TProgressbar',
                                          length=400, height=20)
        self.progress_bar.pack(fill=tk.X, pady=(5, 0))

        # 状态信息
        status_frame = tk.Frame(footer_frame, bg=self.colors['surface'])
        status_frame.pack(fill=tk.X, padx=20, pady=(0, 10))

        self.status_var = tk.StringVar(value="🟢 就绪")
        status_label = tk.Label(status_frame, textvariable=self.status_var,
                              font=('Microsoft YaHei', 10),
                              fg=self.colors['text_light'],
                              bg=self.colors['surface'])
        status_label.pack(side=tk.LEFT)

        # 时间信息
        self.time_var = tk.StringVar(value="")
        time_label = tk.Label(status_frame, textvariable=self.time_var,
                            font=('Microsoft YaHei', 10),
                            fg=self.colors['text_light'],
                            bg=self.colors['surface'])
        time_label.pack(side=tk.RIGHT)
        
    def select_video_folder(self):
        """选择视频文件夹"""
        folder = filedialog.askdirectory(title="选择视频文件夹")
        if folder:
            self.load_video_files(folder)

    def select_audio_folder(self):
        """选择音频文件夹"""
        folder = filedialog.askdirectory(title="选择音频文件夹")
        if folder:
            self.load_audio_files(folder)

    def select_output_folder(self):
        """选择输出文件夹"""
        self.output_folder = filedialog.askdirectory(title="选择输出文件夹")
        if self.output_folder:
            self.status_var.set(f"🟢 输出文件夹: {Path(self.output_folder).name}")

    def load_video_files(self, folder: str):
        """加载视频文件"""
        video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'}
        self.video_files = []

        self.status_var.set("🔍 正在扫描视频文件...")
        self.root.update()

        for file_path in Path(folder).rglob('*'):
            if file_path.suffix.lower() in video_extensions:
                self.video_files.append(str(file_path))

        self.update_video_tree()
        self.update_stats()
        self.status_var.set(f"✅ 已加载 {len(self.video_files)} 个视频文件")

    def load_audio_files(self, folder: str):
        """加载音频文件"""
        audio_extensions = {'.mp3', '.wav', '.aac', '.flac', '.ogg', '.m4a', '.wma'}
        self.audio_files = []

        self.status_var.set("🔍 正在扫描音频文件...")
        self.root.update()

        for file_path in Path(folder).rglob('*'):
            if file_path.suffix.lower() in audio_extensions:
                self.audio_files.append(str(file_path))

        self.update_audio_tree()
        self.update_stats()
        self.status_var.set(f"✅ 已加载 {len(self.audio_files)} 个音频文件")

    def get_file_size(self, file_path: str) -> str:
        """获取文件大小"""
        try:
            size = os.path.getsize(file_path)
            if size < 1024:
                return f"{size} B"
            elif size < 1024 * 1024:
                return f"{size / 1024:.1f} KB"
            elif size < 1024 * 1024 * 1024:
                return f"{size / (1024 * 1024):.1f} MB"
            else:
                return f"{size / (1024 * 1024 * 1024):.1f} GB"
        except:
            return "未知"

    def update_video_tree(self):
        """更新视频文件树形视图"""
        # 清空现有项目
        for item in self.video_tree.get_children():
            self.video_tree.delete(item)

        # 添加视频文件信息
        for file_path in self.video_files:
            path_obj = Path(file_path)
            name = path_obj.name
            size = self.get_file_size(file_path)
            format_ext = path_obj.suffix.upper()[1:]  # 去掉点号
            relative_path = str(path_obj.parent)

            self.video_tree.insert('', 'end', values=(name, size, format_ext, relative_path))

    def update_audio_tree(self):
        """更新音频文件树形视图"""
        # 清空现有项目
        for item in self.audio_tree.get_children():
            self.audio_tree.delete(item)

        # 添加音频文件信息
        for file_path in self.audio_files:
            path_obj = Path(file_path)
            name = path_obj.name
            size = self.get_file_size(file_path)
            format_ext = path_obj.suffix.upper()[1:]  # 去掉点号
            relative_path = str(path_obj.parent)

            self.audio_tree.insert('', 'end', values=(name, size, format_ext, relative_path))

    def update_stats(self):
        """更新统计信息"""
        if hasattr(self, 'stats_labels'):
            self.stats_labels['total_videos'].config(text=str(len(self.video_files)))
            self.stats_labels['total_audios'].config(text=str(len(self.audio_files)))
            self.stats_labels['matched_pairs'].config(text=str(len(self.matches)))

            total_files = len(self.video_files) + len(self.audio_files)
            matched_files = len(self.matches) * 2
            unmatched = total_files - matched_files
            self.stats_labels['unmatched'].config(text=str(max(0, unmatched)))
            
    def start_matching(self):
        """开始文件匹配"""
        if not self.video_files or not self.audio_files:
            messagebox.showwarning("⚠️ 警告", "请先选择视频和音频文件夹")
            return

        # 更新配置
        self.update_merger_config()

        self.status_var.set("🔍 正在智能匹配文件...")
        self.progress_label.config(text="正在分析文件名相似度...")
        self.root.update()

        # 在新线程中执行匹配以避免界面卡顿
        threading.Thread(target=self.matching_thread, daemon=True).start()

    def matching_thread(self):
        """匹配线程"""
        try:
            self.matches = self.merger.find_matching_files(self.video_files, self.audio_files)

            # 在主线程中更新UI
            self.root.after(0, self.update_match_results)

        except Exception as e:
            self.root.after(0, lambda: self.show_error(f"匹配过程中出现错误: {str(e)}"))

    def update_merger_config(self):
        """更新合并器配置"""
        # 更新配置文件中的设置
        self.merger.config['DEFAULT']['match_threshold'] = str(self.threshold_var.get())
        self.merger.config['MATCHING']['ignore_case'] = str(self.ignore_case_var.get())
        self.merger.config['MATCHING']['fuzzy_matching'] = str(self.fuzzy_match_var.get())
        self.merger.config['DEFAULT']['output_format'] = self.format_var.get()
        self.merger.config['DEFAULT']['video_codec'] = self.video_codec_var.get()
        self.merger.config['DEFAULT']['audio_codec'] = self.audio_codec_var.get()

    def update_match_results(self):
        """更新匹配结果显示"""
        # 清空现有结果
        for item in self.match_tree.get_children():
            self.match_tree.delete(item)

        # 添加匹配结果
        for video_file, audio_file in self.matches:
            video_name = Path(video_file).name
            audio_name = Path(audio_file).name

            # 计算相似度
            similarity = self.calculate_similarity(video_name, audio_name)
            status = "✅ 匹配" if similarity >= self.threshold_var.get() else "⚠️ 低匹配"

            self.match_tree.insert('', 'end', values=(
                video_name, audio_name, f"{similarity}%", status
            ))

        # 更新统计信息
        self.update_stats()

        # 更新状态
        self.status_var.set(f"✅ 匹配完成，找到 {len(self.matches)} 对文件")
        self.progress_label.config(text=f"匹配完成 - 成功匹配 {len(self.matches)} 对文件")

        # 如果有匹配结果，显示成功消息
        if self.matches:
            messagebox.showinfo("🎉 匹配成功",
                              f"成功匹配 {len(self.matches)} 对文件！\n点击'开始合并'进行处理。")
        else:
            messagebox.showwarning("⚠️ 无匹配结果",
                                 "未找到匹配的文件对。\n请检查文件名或调整匹配设置。")

    def calculate_similarity(self, name1: str, name2: str) -> int:
        """计算文件名相似度"""
        try:
            from fuzzywuzzy import fuzz
            # 移除扩展名
            name1 = Path(name1).stem
            name2 = Path(name2).stem

            if self.ignore_case_var.get():
                name1 = name1.lower()
                name2 = name2.lower()

            return fuzz.ratio(name1, name2)
        except:
            # 如果fuzzywuzzy不可用，使用简单的字符串比较
            return 100 if name1 == name2 else 0
            
    def start_merging(self):
        """开始合并处理"""
        if not self.matches:
            messagebox.showwarning("⚠️ 警告", "请先进行文件匹配")
            return

        if not self.output_folder:
            messagebox.showwarning("⚠️ 警告", "请选择输出文件夹")
            return

        # 确认开始合并
        result = messagebox.askyesno("🚀 开始合并",
                                   f"准备合并 {len(self.matches)} 对文件\n"
                                   f"输出到: {Path(self.output_folder).name}\n\n"
                                   "确定要开始吗？")
        if not result:
            return

        # 更新配置
        self.update_merger_config()

        # 在新线程中执行合并
        threading.Thread(target=self.merge_thread, daemon=True).start()

    def merge_thread(self):
        """合并线程"""
        import time
        start_time = time.time()

        def progress_callback(current, total):
            progress = (current / total) * 100
            elapsed = time.time() - start_time
            if current > 0:
                eta = (elapsed / current) * (total - current)
                eta_str = f" (预计剩余: {int(eta//60)}:{int(eta%60):02d})"
            else:
                eta_str = ""

            self.root.after(0, lambda: self.progress_var.set(progress))
            self.root.after(0, lambda: self.progress_label.config(
                text=f"正在处理第 {current}/{total} 个文件{eta_str}"))
            self.root.after(0, lambda: self.status_var.set(f"🔄 处理中 {current}/{total}"))

        try:
            self.root.after(0, lambda: self.status_var.set("🚀 开始批量合并..."))
            self.root.after(0, lambda: self.progress_label.config(text="初始化合并进程..."))

            results = self.merger.batch_merge(self.matches, self.output_folder, progress_callback)

            success_count = sum(results.values())
            total_count = len(results)
            elapsed_total = time.time() - start_time

            self.root.after(0, lambda: self.progress_var.set(100))
            self.root.after(0, lambda: self.progress_label.config(
                text=f"合并完成！用时 {int(elapsed_total//60)}:{int(elapsed_total%60):02d}"))
            self.root.after(0, lambda: self.status_var.set(
                f"✅ 合并完成！成功: {success_count}/{total_count}"))

            # 显示结果
            if success_count == total_count:
                self.root.after(0, lambda: messagebox.showinfo(
                    "🎉 合并完成",
                    f"所有 {total_count} 个文件合并成功！\n"
                    f"用时: {int(elapsed_total//60)}:{int(elapsed_total%60):02d}\n"
                    f"输出位置: {self.output_folder}"))
            else:
                failed_count = total_count - success_count
                self.root.after(0, lambda: messagebox.showwarning(
                    "⚠️ 部分失败",
                    f"成功: {success_count} 个\n"
                    f"失败: {failed_count} 个\n\n"
                    "请查看日志文件了解详细错误信息"))

        except Exception as e:
            self.root.after(0, lambda: self.show_error(f"合并过程中出现错误: {str(e)}"))

    def show_error(self, message: str):
        """显示错误信息"""
        self.status_var.set("❌ 处理失败")
        self.progress_label.config(text="处理失败，请查看错误信息")
        messagebox.showerror("❌ 错误", message)
            
    def clear_lists(self):
        """清空所有列表"""
        result = messagebox.askyesno("🗑️ 确认清空", "确定要清空所有文件列表吗？")
        if not result:
            return

        self.video_files = []
        self.audio_files = []
        self.matches = []
        self.output_folder = ""

        # 清空视频文件树
        for item in self.video_tree.get_children():
            self.video_tree.delete(item)

        # 清空音频文件树
        for item in self.audio_tree.get_children():
            self.audio_tree.delete(item)

        # 清空匹配结果树
        for item in self.match_tree.get_children():
            self.match_tree.delete(item)

        # 重置进度和状态
        self.progress_var.set(0)
        self.progress_label.config(text="准备就绪")
        self.status_var.set("🟢 已清空所有列表")

        # 更新统计信息
        self.update_stats()

    def show_about(self):
        """显示关于信息"""
        about_text = """
🎬 音视频合并工具 v1.0.0

✨ 功能特点:
• 智能文件名匹配
• 批量处理支持
• 多种输出格式
• 实时进度显示
• 现代化界面设计

🛠️ 技术支持:
• Python + Tkinter
• FFmpeg 音视频处理
• 模糊匹配算法

📧 如有问题请查看日志文件
        """
        messagebox.showinfo("关于", about_text)

    def on_closing(self):
        """窗口关闭事件"""
        if messagebox.askokcancel("退出", "确定要退出音视频合并工具吗？"):
            self.root.destroy()

    def run(self):
        """运行应用程序"""
        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 添加菜单栏
        self.create_menu()

        # 启动主循环
        self.root.mainloop()

    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="选择视频文件夹", command=self.select_video_folder)
        file_menu.add_command(label="选择音频文件夹", command=self.select_audio_folder)
        file_menu.add_command(label="选择输出文件夹", command=self.select_output_folder)
        file_menu.add_separator()
        file_menu.add_command(label="清空列表", command=self.clear_lists)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.on_closing)

        # 处理菜单
        process_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="处理", menu=process_menu)
        process_menu.add_command(label="开始匹配", command=self.start_matching)
        process_menu.add_command(label="开始合并", command=self.start_merging)

        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="关于", command=self.show_about)


def main():
    """主函数"""
    app = MergerGUI()
    app.run()


if __name__ == "__main__":
    main()
