#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音视频合并软件
支持智能文件名匹配和批量处理
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import queue
import logging
from pathlib import Path
import configparser
from typing import List, Tuple, Dict, Optional
import ffmpeg
from tqdm import tqdm
import difflib
from fuzzywuzzy import fuzz, process

class AudioVideoMerger:
    """音视频合并核心类"""
    
    def __init__(self):
        self.setup_logging()
        self.config = self.load_config()
        
    def setup_logging(self):
        """设置日志系统"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('merger.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def load_config(self) -> configparser.ConfigParser:
        """加载配置文件"""
        config = configparser.ConfigParser()
        config_file = 'config.ini'
        
        if not os.path.exists(config_file):
            # 创建默认配置
            config['DEFAULT'] = {
                'output_format': 'mp4',
                'video_codec': 'libx264',
                'audio_codec': 'aac',
                'quality': 'medium',
                'match_threshold': '80'
            }
            config['MATCHING'] = {
                'ignore_case': 'True',
                'remove_extensions': 'True',
                'fuzzy_matching': 'True'
            }
            
            with open(config_file, 'w', encoding='utf-8') as f:
                config.write(f)
                
        config.read(config_file, encoding='utf-8')
        return config
        
    def find_matching_files(self, video_files: List[str], audio_files: List[str]) -> List[Tuple[str, str]]:
        """智能匹配音频和视频文件"""
        matches = []
        threshold = int(self.config['DEFAULT']['match_threshold'])
        ignore_case = self.config.getboolean('MATCHING', 'ignore_case')
        remove_ext = self.config.getboolean('MATCHING', 'remove_extensions')
        fuzzy = self.config.getboolean('MATCHING', 'fuzzy_matching')
        
        self.logger.info(f"开始匹配文件，阈值: {threshold}")
        
        for video_file in video_files:
            video_name = Path(video_file).stem
            if ignore_case:
                video_name = video_name.lower()
                
            best_match = None
            best_score = 0
            
            for audio_file in audio_files:
                audio_name = Path(audio_file).stem
                if ignore_case:
                    audio_name = audio_name.lower()
                
                # 精确匹配
                if video_name == audio_name:
                    matches.append((video_file, audio_file))
                    self.logger.info(f"精确匹配: {video_file} <-> {audio_file}")
                    break
                    
                # 模糊匹配
                if fuzzy:
                    score = fuzz.ratio(video_name, audio_name)
                    if score > best_score and score >= threshold:
                        best_score = score
                        best_match = audio_file
                        
            else:  # 如果没有精确匹配，使用最佳模糊匹配
                if best_match:
                    matches.append((video_file, best_match))
                    self.logger.info(f"模糊匹配 ({best_score}%): {video_file} <-> {best_match}")
                else:
                    self.logger.warning(f"未找到匹配的音频文件: {video_file}")
                    
        return matches
        
    def merge_audio_video(self, video_path: str, audio_path: str, output_path: str, 
                         progress_callback=None) -> bool:
        """合并单个音频和视频文件"""
        try:
            self.logger.info(f"开始合并: {video_path} + {audio_path} -> {output_path}")
            
            # 获取配置参数
            video_codec = self.config['DEFAULT']['video_codec']
            audio_codec = self.config['DEFAULT']['audio_codec']
            
            # 使用ffmpeg合并
            input_video = ffmpeg.input(video_path)
            input_audio = ffmpeg.input(audio_path)
            
            out = ffmpeg.output(
                input_video['v'], input_audio['a'], 
                output_path,
                vcodec=video_codec,
                acodec=audio_codec,
                **{'c:v': video_codec, 'c:a': audio_codec}
            )
            
            ffmpeg.run(out, overwrite_output=True, quiet=True)
            
            if progress_callback:
                progress_callback()
                
            self.logger.info(f"合并完成: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"合并失败 {video_path} + {audio_path}: {str(e)}")
            return False
            
    def batch_merge(self, matches: List[Tuple[str, str]], output_dir: str, 
                   progress_callback=None) -> Dict[str, bool]:
        """批量合并文件"""
        results = {}
        total = len(matches)
        
        self.logger.info(f"开始批量处理 {total} 个文件对")
        
        for i, (video_file, audio_file) in enumerate(matches):
            video_name = Path(video_file).stem
            output_format = self.config['DEFAULT']['output_format']
            output_file = os.path.join(output_dir, f"{video_name}_merged.{output_format}")
            
            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)
            
            success = self.merge_audio_video(video_file, audio_file, output_file)
            results[f"{video_file}+{audio_file}"] = success
            
            if progress_callback:
                progress_callback(i + 1, total)
                
        self.logger.info(f"批量处理完成，成功: {sum(results.values())}/{total}")
        return results


class MergerGUI:
    """图形用户界面"""
    
    def __init__(self):
        self.merger = AudioVideoMerger()
        self.root = tk.Tk()
        self.setup_ui()
        self.video_files = []
        self.audio_files = []
        self.matches = []
        
    def setup_ui(self):
        """设置用户界面"""
        self.root.title("音视频合并工具")
        self.root.geometry("800x600")
        
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="文件选择", padding="10")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(file_frame, text="选择视频文件夹", 
                  command=self.select_video_folder).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(file_frame, text="选择音频文件夹", 
                  command=self.select_audio_folder).grid(row=0, column=1, padx=(0, 10))
        ttk.Button(file_frame, text="选择输出文件夹", 
                  command=self.select_output_folder).grid(row=0, column=2)
        
        # 文件列表显示
        list_frame = ttk.LabelFrame(main_frame, text="文件列表", padding="10")
        list_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 视频文件列表
        ttk.Label(list_frame, text="视频文件:").grid(row=0, column=0, sticky=tk.W)
        self.video_listbox = tk.Listbox(list_frame, height=8)
        self.video_listbox.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        
        # 音频文件列表
        ttk.Label(list_frame, text="音频文件:").grid(row=0, column=1, sticky=tk.W)
        self.audio_listbox = tk.Listbox(list_frame, height=8)
        self.audio_listbox.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))
        
        # 匹配结果显示
        match_frame = ttk.LabelFrame(main_frame, text="匹配结果", padding="10")
        match_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        self.match_tree = ttk.Treeview(match_frame, columns=('video', 'audio'), show='headings', height=6)
        self.match_tree.heading('video', text='视频文件')
        self.match_tree.heading('audio', text='音频文件')
        self.match_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 控制按钮
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(control_frame, text="开始匹配", 
                  command=self.start_matching).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(control_frame, text="开始合并", 
                  command=self.start_merging).grid(row=0, column=1, padx=(0, 10))
        ttk.Button(control_frame, text="清空列表", 
                  command=self.clear_lists).grid(row=0, column=2)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(main_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        list_frame.columnconfigure(0, weight=1)
        list_frame.columnconfigure(1, weight=1)
        list_frame.rowconfigure(1, weight=1)
        match_frame.columnconfigure(0, weight=1)
        match_frame.rowconfigure(0, weight=1)
        
    def select_video_folder(self):
        """选择视频文件夹"""
        folder = filedialog.askdirectory(title="选择视频文件夹")
        if folder:
            self.load_video_files(folder)
            
    def select_audio_folder(self):
        """选择音频文件夹"""
        folder = filedialog.askdirectory(title="选择音频文件夹")
        if folder:
            self.load_audio_files(folder)
            
    def select_output_folder(self):
        """选择输出文件夹"""
        self.output_folder = filedialog.askdirectory(title="选择输出文件夹")
        if self.output_folder:
            self.status_var.set(f"输出文件夹: {self.output_folder}")
            
    def load_video_files(self, folder: str):
        """加载视频文件"""
        video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'}
        self.video_files = []
        
        for file_path in Path(folder).rglob('*'):
            if file_path.suffix.lower() in video_extensions:
                self.video_files.append(str(file_path))
                
        self.update_video_listbox()
        self.status_var.set(f"已加载 {len(self.video_files)} 个视频文件")
        
    def load_audio_files(self, folder: str):
        """加载音频文件"""
        audio_extensions = {'.mp3', '.wav', '.aac', '.flac', '.ogg', '.m4a', '.wma'}
        self.audio_files = []
        
        for file_path in Path(folder).rglob('*'):
            if file_path.suffix.lower() in audio_extensions:
                self.audio_files.append(str(file_path))
                
        self.update_audio_listbox()
        self.status_var.set(f"已加载 {len(self.audio_files)} 个音频文件")
        
    def update_video_listbox(self):
        """更新视频文件列表"""
        self.video_listbox.delete(0, tk.END)
        for file_path in self.video_files:
            self.video_listbox.insert(tk.END, Path(file_path).name)
            
    def update_audio_listbox(self):
        """更新音频文件列表"""
        self.audio_listbox.delete(0, tk.END)
        for file_path in self.audio_files:
            self.audio_listbox.insert(tk.END, Path(file_path).name)
            
    def start_matching(self):
        """开始文件匹配"""
        if not self.video_files or not self.audio_files:
            messagebox.showwarning("警告", "请先选择视频和音频文件夹")
            return
            
        self.status_var.set("正在匹配文件...")
        self.matches = self.merger.find_matching_files(self.video_files, self.audio_files)
        self.update_match_tree()
        self.status_var.set(f"匹配完成，找到 {len(self.matches)} 对文件")
        
    def update_match_tree(self):
        """更新匹配结果树"""
        for item in self.match_tree.get_children():
            self.match_tree.delete(item)
            
        for video_file, audio_file in self.matches:
            self.match_tree.insert('', 'end', values=(
                Path(video_file).name, 
                Path(audio_file).name
            ))
            
    def start_merging(self):
        """开始合并处理"""
        if not self.matches:
            messagebox.showwarning("警告", "请先进行文件匹配")
            return
            
        if not hasattr(self, 'output_folder') or not self.output_folder:
            messagebox.showwarning("警告", "请选择输出文件夹")
            return
            
        # 在新线程中执行合并
        threading.Thread(target=self.merge_thread, daemon=True).start()
        
    def merge_thread(self):
        """合并线程"""
        def progress_callback(current, total):
            progress = (current / total) * 100
            self.root.after(0, lambda: self.progress_var.set(progress))
            self.root.after(0, lambda: self.status_var.set(f"正在处理 {current}/{total}"))
            
        self.root.after(0, lambda: self.status_var.set("开始批量合并..."))
        
        results = self.merger.batch_merge(self.matches, self.output_folder, progress_callback)
        
        success_count = sum(results.values())
        total_count = len(results)
        
        self.root.after(0, lambda: self.progress_var.set(100))
        self.root.after(0, lambda: self.status_var.set(
            f"合并完成！成功: {success_count}/{total_count}"
        ))
        
        if success_count < total_count:
            self.root.after(0, lambda: messagebox.showwarning(
                "部分失败", f"有 {total_count - success_count} 个文件合并失败，请查看日志"
            ))
        else:
            self.root.after(0, lambda: messagebox.showinfo("完成", "所有文件合并成功！"))
            
    def clear_lists(self):
        """清空所有列表"""
        self.video_files = []
        self.audio_files = []
        self.matches = []
        self.video_listbox.delete(0, tk.END)
        self.audio_listbox.delete(0, tk.END)
        for item in self.match_tree.get_children():
            self.match_tree.delete(item)
        self.progress_var.set(0)
        self.status_var.set("已清空列表")
        
    def run(self):
        """运行应用程序"""
        self.root.mainloop()


def main():
    """主函数"""
    app = MergerGUI()
    app.run()


if __name__ == "__main__":
    main()
