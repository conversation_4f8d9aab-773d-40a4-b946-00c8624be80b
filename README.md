# 音视频合并工具

一个功能强大的音频和视频文件合并软件，支持智能文件名匹配和批量处理。

## 🌟 主要功能

- **智能文件名匹配**：自动根据文件名匹配音频和视频文件
- **批量处理**：支持大量文件的批量合并
- **多种匹配模式**：精确匹配、模糊匹配、忽略大小写
- **进度监控**：实时显示处理进度
- **错误处理**：完善的错误处理和日志记录
- **用户友好界面**：简洁直观的图形界面
- **可配置选项**：支持多种输出格式和编码设置

## 📋 系统要求

- Python 3.7+
- FFmpeg (需要安装在系统PATH中)
- 支持的操作系统：Windows、macOS、Linux

## 🚀 安装步骤

1. **克隆或下载项目**
   ```bash
   git clone <repository-url>
   cd audio-video-merger
   ```

2. **安装Python依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **安装FFmpeg**
   
   **Windows:**
   - 下载FFmpeg: https://ffmpeg.org/download.html
   - 解压并添加到系统PATH
   
   **macOS:**
   ```bash
   brew install ffmpeg
   ```
   
   **Linux (Ubuntu/Debian):**
   ```bash
   sudo apt update
   sudo apt install ffmpeg
   ```

## 🎯 使用方法

### 图形界面模式

1. **启动程序**
   ```bash
   python audio_video_merger.py
   ```

2. **选择文件夹**
   - 点击"选择视频文件夹"选择包含视频文件的文件夹
   - 点击"选择音频文件夹"选择包含音频文件的文件夹
   - 点击"选择输出文件夹"选择合并后文件的保存位置

3. **文件匹配**
   - 点击"开始匹配"进行智能文件名匹配
   - 查看匹配结果列表

4. **开始合并**
   - 点击"开始合并"开始批量处理
   - 观察进度条和状态信息

### 命令行模式

```python
from audio_video_merger import AudioVideoMerger

# 创建合并器实例
merger = AudioVideoMerger()

# 获取文件列表
video_files = ['video1.mp4', 'video2.mp4']
audio_files = ['audio1.mp3', 'audio2.mp3']

# 智能匹配
matches = merger.find_matching_files(video_files, audio_files)

# 批量合并
results = merger.batch_merge(matches, 'output_folder')
```

## ⚙️ 配置选项

编辑 `config.ini` 文件来自定义设置：

```ini
[DEFAULT]
output_format = mp4          # 输出格式
video_codec = libx264        # 视频编码器
audio_codec = aac           # 音频编码器
match_threshold = 80        # 匹配阈值 (0-100)

[MATCHING]
ignore_case = True          # 忽略大小写
fuzzy_matching = True       # 启用模糊匹配
```

## 📁 支持的文件格式

**视频格式：**
- MP4, AVI, MOV, MKV, WMV, FLV, WebM

**音频格式：**
- MP3, WAV, AAC, FLAC, OGG, M4A, WMA

## 🔧 文件名匹配规则

软件支持多种智能匹配模式：

1. **精确匹配**：文件名完全相同（忽略扩展名）
2. **模糊匹配**：使用相似度算法匹配相似的文件名
3. **忽略大小写**：不区分文件名的大小写
4. **自定义阈值**：可调整模糊匹配的相似度要求

### 匹配示例

```
视频文件: "Episode_01.mp4"
音频文件: "episode_01.mp3"  ✅ 匹配成功

视频文件: "Movie_Part1.avi"
音频文件: "Movie Part 1.wav"  ✅ 模糊匹配成功

视频文件: "Video123.mp4"
音频文件: "Audio456.mp3"  ❌ 匹配失败
```

## 📊 批量处理特性

- **并发处理**：支持多线程处理提高效率
- **进度监控**：实时显示处理进度和剩余时间
- **错误恢复**：单个文件失败不影响其他文件处理
- **详细日志**：记录所有操作和错误信息

## 🐛 故障排除

### 常见问题

1. **FFmpeg未找到**
   - 确保FFmpeg已正确安装并添加到系统PATH
   - 在命令行中运行 `ffmpeg -version` 验证安装

2. **文件匹配失败**
   - 检查文件名是否相似
   - 调整配置文件中的 `match_threshold` 值
   - 启用模糊匹配选项

3. **合并失败**
   - 检查输入文件是否损坏
   - 确保有足够的磁盘空间
   - 查看 `merger.log` 文件获取详细错误信息

### 日志文件

程序会自动生成 `merger.log` 日志文件，包含：
- 文件匹配过程
- 合并操作详情
- 错误信息和堆栈跟踪

## 🤝 贡献

欢迎提交问题报告和功能请求！

## 📄 许可证

本项目采用 MIT 许可证。

## 🔄 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的音视频合并功能
- 智能文件名匹配
- 图形用户界面
- 批量处理支持
