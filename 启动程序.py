#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音视频合并工具 - 一键启动脚本
"""

import sys
import os
import subprocess
import tkinter as tk
from tkinter import messagebox

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        messagebox.showerror("版本错误", 
                           f"需要Python 3.7或更高版本\n"
                           f"当前版本: {sys.version_info.major}.{sys.version_info.minor}")
        return False
    return True

def check_dependencies():
    """检查依赖包"""
    missing_packages = []
    
    # 检查基本包
    try:
        import tkinter
    except ImportError:
        missing_packages.append("tkinter")
    
    # 检查其他包（可选）
    optional_packages = ['ffmpeg', 'tqdm', 'fuzzywuzzy']
    missing_optional = []
    
    for package in optional_packages:
        try:
            if package == 'ffmpeg':
                import ffmpeg
            elif package == 'tqdm':
                import tqdm
            elif package == 'fuzzywuzzy':
                import fuzzywuzzy
        except ImportError:
            missing_optional.append(package)
    
    if missing_packages:
        messagebox.showerror("缺少依赖", 
                           f"缺少必需的包: {', '.join(missing_packages)}\n"
                           "请安装Python的tkinter包")
        return False
    
    if missing_optional:
        result = messagebox.askyesno("可选依赖", 
                                   f"缺少可选包: {', '.join(missing_optional)}\n"
                                   f"这些包用于高级功能\n\n"
                                   f"是否继续启动程序？\n"
                                   f"(可以稍后通过 pip install -r requirements.txt 安装)")
        return result
    
    return True

def check_ffmpeg():
    """检查FFmpeg"""
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=5)
        return result.returncode == 0
    except:
        return False

def show_startup_dialog():
    """显示启动选择对话框"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    # 设置对话框
    dialog = tk.Toplevel(root)
    dialog.title("🎬 音视频合并工具")
    dialog.geometry("400x300")
    dialog.resizable(False, False)
    
    # 居中显示
    dialog.transient(root)
    dialog.grab_set()
    
    # 标题
    title_label = tk.Label(dialog, 
                          text="🎬 音视频合并工具",
                          font=('Microsoft YaHei', 16, 'bold'),
                          fg='#2E86AB')
    title_label.pack(pady=20)
    
    # 说明
    info_label = tk.Label(dialog,
                         text="选择启动方式：",
                         font=('Microsoft YaHei', 12))
    info_label.pack(pady=10)
    
    # 按钮框架
    button_frame = tk.Frame(dialog)
    button_frame.pack(pady=20)
    
    def start_demo():
        dialog.destroy()
        root.destroy()
        start_program("demo")
    
    def start_full():
        dialog.destroy()
        root.destroy()
        start_program("full")
    
    def start_simple():
        dialog.destroy()
        root.destroy()
        start_program("simple")
    
    # 按钮
    tk.Button(button_frame, 
             text="🎨 界面演示\n(查看界面效果)",
             font=('Microsoft YaHei', 10),
             bg='#E3F2FD',
             fg='#2E86AB',
             width=15,
             height=3,
             command=start_demo).pack(pady=5)
    
    tk.Button(button_frame,
             text="🚀 完整版本\n(全部功能)",
             font=('Microsoft YaHei', 10),
             bg='#2E86AB',
             fg='white',
             width=15,
             height=3,
             command=start_full).pack(pady=5)
    
    tk.Button(button_frame,
             text="⚡ 简化版本\n(基本功能)",
             font=('Microsoft YaHei', 10),
             bg='#A23B72',
             fg='white',
             width=15,
             height=3,
             command=start_simple).pack(pady=5)
    
    # 帮助信息
    help_label = tk.Label(dialog,
                         text="💡 首次使用建议选择'界面演示'",
                         font=('Microsoft YaHei', 9),
                         fg='#666666')
    help_label.pack(pady=(20, 10))
    
    # 运行对话框
    root.mainloop()

def start_program(mode):
    """启动程序"""
    try:
        if mode == "demo":
            print("🎨 启动界面演示...")
            subprocess.run([sys.executable, 'demo_gui.py'])
        elif mode == "full":
            print("🚀 启动完整版本...")
            subprocess.run([sys.executable, 'audio_video_merger.py'])
        elif mode == "simple":
            print("⚡ 启动简化版本...")
            # 创建简化版本
            create_simple_version()
        else:
            print("❌ 未知的启动模式")
    except Exception as e:
        messagebox.showerror("启动失败", f"程序启动失败:\n{str(e)}")

def create_simple_version():
    """创建简化版本"""
    try:
        import tkinter as tk
        from tkinter import filedialog, messagebox
        import os
        from pathlib import Path
        
        class SimpleGUI:
            def __init__(self):
                self.root = tk.Tk()
                self.root.title("🎬 音视频合并工具 - 简化版")
                self.root.geometry("600x400")
                
                # 创建界面
                tk.Label(self.root, text="🎬 音视频合并工具", 
                        font=('Microsoft YaHei', 16, 'bold')).pack(pady=20)
                
                tk.Label(self.root, text="简化版本 - 基本功能演示", 
                        font=('Microsoft YaHei', 10)).pack(pady=5)
                
                # 按钮
                tk.Button(self.root, text="选择视频文件夹", 
                         font=('Microsoft YaHei', 12),
                         bg='#2E86AB', fg='white',
                         command=self.select_folder).pack(pady=10)
                
                tk.Button(self.root, text="选择音频文件夹", 
                         font=('Microsoft YaHei', 12),
                         bg='#2E86AB', fg='white',
                         command=self.select_folder).pack(pady=10)
                
                tk.Button(self.root, text="开始处理", 
                         font=('Microsoft YaHei', 12),
                         bg='#F18F01', fg='white',
                         command=self.process).pack(pady=20)
                
                # 状态
                self.status = tk.Label(self.root, text="准备就绪", 
                                     font=('Microsoft YaHei', 10))
                self.status.pack(pady=10)
                
            def select_folder(self):
                folder = filedialog.askdirectory()
                if folder:
                    self.status.config(text=f"已选择: {Path(folder).name}")
                    
            def process(self):
                messagebox.showinfo("简化版本", 
                                  "这是简化版本的演示\n"
                                  "完整功能请使用完整版本")
                
            def run(self):
                self.root.mainloop()
        
        app = SimpleGUI()
        app.run()
        
    except Exception as e:
        messagebox.showerror("错误", f"简化版本启动失败:\n{str(e)}")

def main():
    """主函数"""
    print("🎬 音视频合并工具启动器")
    print("=" * 40)
    
    # 检查Python版本
    if not check_python_version():
        return
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 检查FFmpeg（可选）
    has_ffmpeg = check_ffmpeg()
    if not has_ffmpeg:
        result = messagebox.askyesno("FFmpeg未找到", 
                                   "未检测到FFmpeg，某些功能可能无法使用\n"
                                   "是否继续启动程序？\n\n"
                                   "建议安装FFmpeg以获得完整功能")
        if not result:
            return
    
    # 显示启动对话框
    show_startup_dialog()

if __name__ == "__main__":
    main()
