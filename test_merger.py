#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音视频合并软件测试文件
"""

import unittest
import tempfile
import os
import shutil
from pathlib import Path
from audio_video_merger import AudioVideoMerger

class TestAudioVideoMerger(unittest.TestCase):
    """音视频合并器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.merger = AudioVideoMerger()
        self.test_dir = tempfile.mkdtemp()
        
        # 创建测试文件名
        self.video_files = [
            os.path.join(self.test_dir, "episode_01.mp4"),
            os.path.join(self.test_dir, "Episode_02.MP4"),
            os.path.join(self.test_dir, "movie_part1.avi"),
            os.path.join(self.test_dir, "Video123.mkv"),
        ]
        
        self.audio_files = [
            os.path.join(self.test_dir, "episode_01.mp3"),
            os.path.join(self.test_dir, "episode_02.wav"),
            os.path.join(self.test_dir, "Movie Part 1.aac"),
            os.path.join(self.test_dir, "Audio456.flac"),
        ]
        
        # 创建空的测试文件
        for file_path in self.video_files + self.audio_files:
            Path(file_path).touch()
            
    def tearDown(self):
        """测试后清理"""
        shutil.rmtree(self.test_dir)
        
    def test_exact_matching(self):
        """测试精确匹配"""
        matches = self.merger.find_matching_files(
            [self.video_files[0]], [self.audio_files[0]]
        )
        self.assertEqual(len(matches), 1)
        self.assertIn((self.video_files[0], self.audio_files[0]), matches)
        
    def test_case_insensitive_matching(self):
        """测试忽略大小写匹配"""
        matches = self.merger.find_matching_files(
            [self.video_files[1]], [self.audio_files[1]]
        )
        self.assertEqual(len(matches), 1)
        
    def test_fuzzy_matching(self):
        """测试模糊匹配"""
        matches = self.merger.find_matching_files(
            [self.video_files[2]], [self.audio_files[2]]
        )
        self.assertEqual(len(matches), 1)
        
    def test_no_matching(self):
        """测试无匹配情况"""
        matches = self.merger.find_matching_files(
            [self.video_files[3]], [self.audio_files[3]]
        )
        self.assertEqual(len(matches), 0)
        
    def test_batch_matching(self):
        """测试批量匹配"""
        matches = self.merger.find_matching_files(
            self.video_files, self.audio_files
        )
        # 应该至少匹配到3对文件
        self.assertGreaterEqual(len(matches), 3)
        
    def test_config_loading(self):
        """测试配置加载"""
        config = self.merger.config
        self.assertIsNotNone(config)
        self.assertEqual(config['DEFAULT']['output_format'], 'mp4')
        self.assertEqual(config['DEFAULT']['match_threshold'], '80')


class TestFileMatching(unittest.TestCase):
    """文件匹配算法测试"""
    
    def setUp(self):
        self.merger = AudioVideoMerger()
        
    def test_matching_algorithms(self):
        """测试各种匹配算法"""
        test_cases = [
            # (视频文件名, 音频文件名, 是否应该匹配)
            ("episode_01", "episode_01", True),
            ("Episode_01", "episode_01", True),
            ("movie_part1", "Movie Part 1", True),
            ("video123", "audio456", False),
            ("test_file", "test-file", True),
            ("Season1_Episode1", "S01E01", False),  # 需要更高级的匹配
        ]
        
        for video_name, audio_name, should_match in test_cases:
            video_files = [f"{video_name}.mp4"]
            audio_files = [f"{audio_name}.mp3"]
            
            matches = self.merger.find_matching_files(video_files, audio_files)
            
            if should_match:
                self.assertEqual(len(matches), 1, 
                    f"应该匹配: {video_name} <-> {audio_name}")
            else:
                self.assertEqual(len(matches), 0, 
                    f"不应该匹配: {video_name} <-> {audio_name}")


def create_sample_files():
    """创建示例文件用于手动测试"""
    sample_dir = "sample_files"
    os.makedirs(sample_dir, exist_ok=True)
    
    video_dir = os.path.join(sample_dir, "videos")
    audio_dir = os.path.join(sample_dir, "audios")
    output_dir = os.path.join(sample_dir, "output")
    
    os.makedirs(video_dir, exist_ok=True)
    os.makedirs(audio_dir, exist_ok=True)
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建示例视频文件名
    video_files = [
        "第一集.mp4",
        "第二集.avi",
        "Episode_03.mkv",
        "movie_part1.mp4",
        "测试视频.mov"
    ]
    
    # 创建示例音频文件名
    audio_files = [
        "第一集.mp3",
        "第二集.wav",
        "Episode_03.aac",
        "Movie Part 1.flac",
        "其他音频.mp3"
    ]
    
    # 创建空文件
    for filename in video_files:
        Path(os.path.join(video_dir, filename)).touch()
        
    for filename in audio_files:
        Path(os.path.join(audio_dir, filename)).touch()
        
    print(f"示例文件已创建在 {sample_dir} 目录中")
    print(f"视频文件: {video_dir}")
    print(f"音频文件: {audio_dir}")
    print(f"输出目录: {output_dir}")


def run_manual_test():
    """运行手动测试"""
    print("=== 音视频合并软件手动测试 ===")
    
    # 创建示例文件
    create_sample_files()
    
    # 测试匹配功能
    merger = AudioVideoMerger()
    
    video_dir = "sample_files/videos"
    audio_dir = "sample_files/audios"
    
    # 获取文件列表
    video_files = []
    audio_files = []
    
    for file_path in Path(video_dir).glob('*'):
        if file_path.is_file():
            video_files.append(str(file_path))
            
    for file_path in Path(audio_dir).glob('*'):
        if file_path.is_file():
            audio_files.append(str(file_path))
    
    print(f"\n找到视频文件 {len(video_files)} 个:")
    for f in video_files:
        print(f"  - {Path(f).name}")
        
    print(f"\n找到音频文件 {len(audio_files)} 个:")
    for f in audio_files:
        print(f"  - {Path(f).name}")
    
    # 执行匹配
    matches = merger.find_matching_files(video_files, audio_files)
    
    print(f"\n匹配结果 ({len(matches)} 对):")
    for video_file, audio_file in matches:
        print(f"  ✅ {Path(video_file).name} <-> {Path(audio_file).name}")
    
    # 显示未匹配的文件
    matched_videos = {v for v, a in matches}
    matched_audios = {a for v, a in matches}
    
    unmatched_videos = set(video_files) - matched_videos
    unmatched_audios = set(audio_files) - matched_audios
    
    if unmatched_videos:
        print(f"\n未匹配的视频文件:")
        for f in unmatched_videos:
            print(f"  ❌ {Path(f).name}")
            
    if unmatched_audios:
        print(f"\n未匹配的音频文件:")
        for f in unmatched_audios:
            print(f"  ❌ {Path(f).name}")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "manual":
        run_manual_test()
    else:
        # 运行单元测试
        unittest.main()
